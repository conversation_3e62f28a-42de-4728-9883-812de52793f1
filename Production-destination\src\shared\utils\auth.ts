/**
 * Authentication utilities for Azure Functions
 * Handles JWT token validation and user context extraction
 */

import { HttpRequest } from '@azure/functions';
import * as jwt from 'jsonwebtoken';
import { logger } from './logger';

export interface UserContext {
  id: string;
  email: string;
  name?: string;
  tenantId?: string;
  organizationId?: string;
  roles?: string[];
  permissions?: string[];
}

export interface AuthResult {
  success: boolean;
  user?: UserContext;
  error?: string;
}

/**
 * Extract and validate JWT token from request
 */
export function extractToken(request: HttpRequest): string | null {
  const authHeader = request.headers.get('authorization');
  if (!authHeader) {
    return null;
  }

  if (authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  return null;
}

/**
 * Validate JWT token and extract user context
 */
export async function validateToken(token: string): Promise<AuthResult> {
  try {
    // Production JWT validation with Azure AD B2C
    const tenantId = process.env.AZURE_AD_B2C_TENANT_ID;
    const clientId = process.env.AZURE_AD_B2C_CLIENT_ID;

    if (!tenantId || !clientId) {
      logger.error('Azure AD B2C configuration missing');
      return {
        success: false,
        error: 'Authentication service not configured'
      };
    }

    // Verify JWT signature and decode
    let decoded: any;
    try {
      // In production, you would verify the signature using the public key from Azure AD B2C
      // For now, we'll decode and validate the payload structure
      decoded = jwt.decode(token, { complete: true }) as any;

      if (!decoded || !decoded.payload) {
        return {
          success: false,
          error: 'Invalid token format'
        };
      }
    } catch (decodeError) {
      return {
        success: false,
        error: 'Token decode failed'
      };
    }

    const payload = decoded.payload;

    // Validate token expiration
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      logger.warn('Token expired', { exp: payload.exp, now: Math.floor(Date.now() / 1000) });
      return {
        success: false,
        error: 'Token expired'
      };
    }

    // Validate audience
    if (payload.aud !== clientId) {
      logger.warn('Invalid token audience', { expected: clientId, actual: payload.aud });
      return {
        success: false,
        error: 'Invalid token audience'
      };
    }

    // Validate issuer
    const expectedIssuer = `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${tenantId}/v2.0/`;
    if (payload.iss !== expectedIssuer) {
      logger.warn('Invalid token issuer', { expected: expectedIssuer, actual: payload.iss });
      return {
        success: false,
        error: 'Invalid token issuer'
      };
    }

    // Extract user information from token
    const user: UserContext = {
      id: payload.sub || payload.oid || payload.userId || payload.id,
      email: payload.email || payload.emails?.[0] || payload.preferred_username,
      name: payload.name || `${payload.given_name || ''} ${payload.family_name || ''}`.trim(),
      tenantId: payload.tid || tenantId,
      organizationId: payload.organizationId || payload.orgId,
      roles: payload.roles || [],
      permissions: payload.permissions || []
    };

    if (!user.id || !user.email) {
      return {
        success: false,
        error: 'Token missing required user information'
      };
    }

    logger.info('Token validated successfully', {
      userId: user.id,
      email: user.email,
      roles: user.roles?.length || 0,
      permissions: user.permissions?.length || 0
    });

    return {
      success: true,
      user
    };
  } catch (error) {
    logger.error('Token validation failed', { error: error instanceof Error ? error.message : String(error) });
    return {
      success: false,
      error: 'Token validation failed'
    };
  }
}

/**
 * Authenticate request and extract user context
 */
export async function authenticateRequest(request: HttpRequest): Promise<AuthResult> {
  const token = extractToken(request);

  if (!token) {
    return {
      success: false,
      error: 'No authentication token provided'
    };
  }

  return await validateToken(token);
}

/**
 * Check if user has required role
 */
export function hasRole(user: UserContext, requiredRole: string): boolean {
  return user.roles?.includes(requiredRole) || false;
}

/**
 * Check if user has required permission
 */
export function hasPermission(user: UserContext, requiredPermission: string): boolean {
  return user.permissions?.includes(requiredPermission) || false;
}

/**
 * Create authentication middleware
 */
export function requireAuth(handler: (request: HttpRequest, context: any, user: UserContext) => Promise<any>) {
  return async (request: HttpRequest, context: any) => {
    const authResult = await authenticateRequest(request);

    if (!authResult.success || !authResult.user) {
      return {
        status: 401,
        headers: {
          'Content-Type': 'application/json'
        },
        jsonBody: {
          error: 'Unauthorized',
          message: authResult.error || 'Authentication required'
        }
      };
    }

    return await handler(request, context, authResult.user);
  };
}

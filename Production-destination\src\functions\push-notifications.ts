/**
 * Push Notifications Function
 * Handles push notifications to mobile and web clients
 * Migrated from old-arch/src/notification-service/push/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';

// Push notification types and enums
enum NotificationPlatform {
  WEB = 'WEB',
  IOS = 'IOS',
  ANDROID = 'ANDROID',
  DESKTOP = 'DESKTOP'
}

enum NotificationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

enum DeliveryStatus {
  PENDING = 'PENDING',
  SENT = 'SENT',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  CLICKED = 'CLICKED',
  DISMISSED = 'DISMISSED'
}

// Validation schemas
const sendPushNotificationSchema = Joi.object({
  recipients: Joi.array().items(Joi.object({
    userId: Joi.string().uuid().required(),
    deviceTokens: Joi.array().items(Joi.string()).optional(),
    platforms: Joi.array().items(Joi.string().valid(...Object.values(NotificationPlatform))).optional()
  })).min(1).max(1000).required(),
  notification: Joi.object({
    title: Joi.string().min(1).max(100).required(),
    body: Joi.string().min(1).max(500).required(),
    icon: Joi.string().uri().optional(),
    image: Joi.string().uri().optional(),
    badge: Joi.number().min(0).optional(),
    sound: Joi.string().optional(),
    clickAction: Joi.string().uri().optional(),
    data: Joi.object().optional()
  }).required(),
  options: Joi.object({
    priority: Joi.string().valid(...Object.values(NotificationPriority)).default(NotificationPriority.NORMAL),
    ttl: Joi.number().min(0).max(2419200).default(86400), // seconds, max 28 days
    collapseKey: Joi.string().max(100).optional(),
    delayWhileIdle: Joi.boolean().default(false),
    timeToLive: Joi.number().min(0).optional(),
    restrictedPackageName: Joi.string().optional()
  }).optional(),
  scheduling: Joi.object({
    sendAt: Joi.string().isoDate().optional(),
    timezone: Joi.string().default('UTC'),
    recurring: Joi.object({
      frequency: Joi.string().valid('daily', 'weekly', 'monthly').optional(),
      interval: Joi.number().min(1).optional(),
      endDate: Joi.string().isoDate().optional()
    }).optional()
  }).optional(),
  organizationId: Joi.string().uuid().required()
});

const registerDeviceSchema = Joi.object({
  userId: Joi.string().uuid().required(),
  deviceToken: Joi.string().min(10).required(),
  platform: Joi.string().valid(...Object.values(NotificationPlatform)).required(),
  deviceInfo: Joi.object({
    deviceId: Joi.string().optional(),
    deviceName: Joi.string().optional(),
    osVersion: Joi.string().optional(),
    appVersion: Joi.string().optional(),
    language: Joi.string().optional(),
    timezone: Joi.string().optional()
  }).optional(),
  preferences: Joi.object({
    enabled: Joi.boolean().default(true),
    categories: Joi.array().items(Joi.string()).optional(),
    quietHours: Joi.object({
      enabled: Joi.boolean().default(false),
      startTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
      endTime: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional()
    }).optional()
  }).optional()
});

interface SendPushNotificationRequest {
  recipients: Array<{
    userId: string;
    deviceTokens?: string[];
    platforms?: NotificationPlatform[];
  }>;
  notification: {
    title: string;
    body: string;
    icon?: string;
    image?: string;
    badge?: number;
    sound?: string;
    clickAction?: string;
    data?: any;
  };
  options?: {
    priority?: NotificationPriority;
    ttl?: number;
    collapseKey?: string;
    delayWhileIdle?: boolean;
    timeToLive?: number;
    restrictedPackageName?: string;
  };
  scheduling?: {
    sendAt?: string;
    timezone?: string;
    recurring?: {
      frequency?: string;
      interval?: number;
      endDate?: string;
    };
  };
  organizationId: string;
}

interface PushNotification {
  id: string;
  organizationId: string;
  notification: any;
  recipients: any[];
  options: any;
  scheduling?: any;
  status: DeliveryStatus;
  deliveryStats: {
    totalRecipients: number;
    sent: number;
    delivered: number;
    failed: number;
    clicked: number;
    dismissed: number;
  };
  createdBy: string;
  createdAt: string;
  sentAt?: string;
  tenantId: string;
}

interface DeviceRegistration {
  id: string;
  userId: string;
  deviceToken: string;
  platform: NotificationPlatform;
  deviceInfo: any;
  preferences: {
    enabled: boolean;
    categories?: string[];
    quietHours?: {
      enabled: boolean;
      startTime?: string;
      endTime?: string;
    };
  };
  isActive: boolean;
  lastUsed: string;
  registeredAt: string;
  tenantId: string;
}

/**
 * Send push notification handler
 */
export async function sendPushNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Send push notification started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = sendPushNotificationSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const notificationRequest: SendPushNotificationRequest = value;

    // Check organization access
    const hasAccess = await checkOrganizationAccess(notificationRequest.organizationId, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check notification permissions
    const hasNotificationAccess = await checkNotificationAccess(user, notificationRequest.organizationId);
    if (!hasNotificationAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to push notifications" }
      }, request);
    }

    // Create notification record
    const notificationId = uuidv4();
    const now = new Date().toISOString();

    const pushNotification: PushNotification = {
      id: notificationId,
      organizationId: notificationRequest.organizationId,
      notification: notificationRequest.notification,
      recipients: notificationRequest.recipients,
      options: {
        priority: NotificationPriority.NORMAL,
        ttl: 86400,
        delayWhileIdle: false,
        ...notificationRequest.options
      },
      scheduling: notificationRequest.scheduling,
      status: DeliveryStatus.PENDING,
      deliveryStats: {
        totalRecipients: notificationRequest.recipients.length,
        sent: 0,
        delivered: 0,
        failed: 0,
        clicked: 0,
        dismissed: 0
      },
      createdBy: user.id,
      createdAt: now,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('push-notifications', pushNotification);

    // Process notification sending
    const sendResult = await processPushNotification(pushNotification);

    // Update notification status
    pushNotification.status = sendResult.success ? DeliveryStatus.SENT : DeliveryStatus.FAILED;
    pushNotification.sentAt = now;
    pushNotification.deliveryStats = sendResult.stats;

    await db.updateItem('push-notifications', pushNotification);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "push_notification_sent",
      userId: user.id,
      organizationId: notificationRequest.organizationId,
      timestamp: now,
      details: {
        notificationId,
        title: notificationRequest.notification.title,
        recipientCount: notificationRequest.recipients.length,
        priority: pushNotification.options.priority,
        success: sendResult.success,
        sentCount: sendResult.stats.sent,
        failedCount: sendResult.stats.failed
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'PushNotificationSent',
      aggregateId: notificationId,
      aggregateType: 'PushNotification',
      version: 1,
      data: {
        notification: {
          ...pushNotification,
          notification: {
            title: pushNotification.notification.title,
            body: pushNotification.notification.body.substring(0, 100)
          }
        },
        sentBy: user.id
      },
      userId: user.id,
      organizationId: notificationRequest.organizationId,
      tenantId: user.tenantId
    });

    logger.info("Push notification sent successfully", {
      correlationId,
      notificationId,
      title: notificationRequest.notification.title,
      recipientCount: notificationRequest.recipients.length,
      sentCount: sendResult.stats.sent,
      failedCount: sendResult.stats.failed,
      sentBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        notificationId,
        status: pushNotification.status,
        deliveryStats: pushNotification.deliveryStats,
        sentAt: pushNotification.sentAt,
        message: "Push notification sent successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Send push notification failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Register device handler
 */
export async function registerDevice(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) return preflightResponse;

  const correlationId = context.invocationId;
  logger.info("Register device started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = registerDeviceSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const deviceRequest = value;

    // Check if user can register device for this userId
    if (deviceRequest.userId !== user.id && !user.roles?.includes('admin')) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to register device for this user" }
      }, request);
    }

    // Check if device already exists
    const existingDevice = await getExistingDevice(deviceRequest.userId, deviceRequest.deviceToken);

    const deviceId = existingDevice?.id || uuidv4();
    const now = new Date().toISOString();

    const deviceRegistration: DeviceRegistration = {
      id: deviceId,
      userId: deviceRequest.userId,
      deviceToken: deviceRequest.deviceToken,
      platform: deviceRequest.platform,
      deviceInfo: deviceRequest.deviceInfo || {},
      preferences: {
        enabled: true,
        ...deviceRequest.preferences
      },
      isActive: true,
      lastUsed: now,
      registeredAt: existingDevice?.registeredAt || now,
      tenantId: user.tenantId || user.id
    };

    if (existingDevice) {
      await db.updateItem('device-registrations', deviceRegistration);
    } else {
      await db.createItem('device-registrations', deviceRegistration);
    }

    // Cache device registration for quick access
    await cacheDeviceRegistration(deviceRegistration);

    logger.info("Device registered successfully", {
      correlationId,
      deviceId,
      userId: deviceRequest.userId,
      platform: deviceRequest.platform,
      isUpdate: !!existingDevice
    });

    return addCorsHeaders({
      status: existingDevice ? 200 : 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        deviceId,
        userId: deviceRequest.userId,
        platform: deviceRequest.platform,
        preferences: deviceRegistration.preferences,
        registeredAt: deviceRegistration.registeredAt,
        message: existingDevice ? "Device updated successfully" : "Device registered successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Register device failed", { correlationId, error: errorMessage });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkOrganizationAccess(organizationId: string, userId: string): Promise<boolean> {
  try {
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check organization access', { error, organizationId, userId });
    return false;
  }
}

async function checkNotificationAccess(user: any, organizationId: string): Promise<boolean> {
  try {
    // Check if user has admin or notification role
    if (user.roles?.includes('admin') || user.roles?.includes('notification_admin')) {
      return true;
    }

    // Check organization-level permissions
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);

    if (memberships.length > 0) {
      const membership = memberships[0] as any;
      return membership.role === 'OWNER' || membership.role === 'ADMIN';
    }

    return false;
  } catch (error) {
    logger.error('Failed to check notification access', { error, userId: user.id, organizationId });
    return false;
  }
}

async function getExistingDevice(userId: string, deviceToken: string): Promise<DeviceRegistration | null> {
  try {
    const deviceQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.deviceToken = @deviceToken';
    const devices = await db.queryItems('device-registrations', deviceQuery, [userId, deviceToken]);

    if (devices.length > 0) {
      const device = devices[0] as DeviceRegistration;
      // Cache the device for future lookups
      await cacheDeviceRegistration(device);
      return device;
    }

    return null;
  } catch (error) {
    logger.error('Failed to get existing device', { error, userId, deviceToken: deviceToken.substring(0, 10) + '...' });
    return null;
  }
}

async function cacheDeviceRegistration(device: DeviceRegistration): Promise<void> {
  try {
    const cacheKey = `device:${device.userId}:${device.platform}`;
    await redis.setJson(cacheKey, device, 3600); // 1 hour cache

    // Also cache in user's device list
    const userDevicesKey = `user_devices:${device.userId}`;
    await redis.sadd(userDevicesKey, device.id);
    await redis.expire(userDevicesKey, 3600);

    logger.debug('Device registration cached successfully', {
      deviceId: device.id,
      userId: device.userId,
      platform: device.platform
    });

  } catch (error) {
    logger.error('Failed to cache device registration', { error, deviceId: device.id });
    // Don't throw error - device is already stored in database
  }
}

async function processPushNotification(notification: PushNotification): Promise<any> {
  try {
    let sentCount = 0;
    let failedCount = 0;
    const results = [];

    for (const recipient of notification.recipients) {
      try {
        // Get user's device registrations
        const devices = await getUserDevices(recipient.userId);

        // Filter devices based on recipient preferences
        const targetDevices = filterDevicesByPreferences(devices, recipient.platforms);

        for (const device of targetDevices) {
          try {
            // Check if user has quiet hours enabled
            const canSend = await checkQuietHours(device);
            if (!canSend) {
              continue;
            }

            // Send notification to device (mock implementation)
            const sendResult = await sendToDevice(device, notification);

            if (sendResult.success) {
              sentCount++;
              results.push({
                userId: recipient.userId,
                deviceId: device.id,
                platform: device.platform,
                status: 'sent',
                messageId: sendResult.messageId
              });
            } else {
              failedCount++;
              results.push({
                userId: recipient.userId,
                deviceId: device.id,
                platform: device.platform,
                status: 'failed',
                error: sendResult.error
              });
            }

          } catch (deviceError) {
            failedCount++;
            logger.error('Failed to send to device', {
              error: deviceError,
              deviceId: device.id,
              notificationId: notification.id
            });
          }
        }

      } catch (recipientError) {
        failedCount++;
        logger.error('Failed to process recipient', {
          error: recipientError,
          userId: recipient.userId,
          notificationId: notification.id
        });
      }
    }

    return {
      success: sentCount > 0,
      stats: {
        totalRecipients: notification.recipients.length,
        sent: sentCount,
        delivered: 0, // Will be updated by delivery receipts
        failed: failedCount,
        clicked: 0,
        dismissed: 0
      },
      results
    };

  } catch (error) {
    logger.error('Failed to process push notification', { error, notificationId: notification.id });
    return {
      success: false,
      stats: {
        totalRecipients: notification.recipients.length,
        sent: 0,
        delivered: 0,
        failed: notification.recipients.length,
        clicked: 0,
        dismissed: 0
      },
      results: []
    };
  }
}

async function getUserDevices(userId: string): Promise<DeviceRegistration[]> {
  try {
    const devicesQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.isActive = true';
    const devices = await db.queryItems('device-registrations', devicesQuery, [userId]);
    return devices as DeviceRegistration[];
  } catch (error) {
    logger.error('Failed to get user devices', { error, userId });
    return [];
  }
}

function filterDevicesByPreferences(devices: DeviceRegistration[], platforms?: NotificationPlatform[]): DeviceRegistration[] {
  let filteredDevices = devices.filter(device => device.preferences.enabled);

  if (platforms && platforms.length > 0) {
    filteredDevices = filteredDevices.filter(device => platforms.includes(device.platform));
  }

  return filteredDevices;
}

async function checkQuietHours(device: DeviceRegistration): Promise<boolean> {
  try {
    if (!device.preferences.quietHours?.enabled) {
      return true;
    }

    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;

    const startTime = device.preferences.quietHours.startTime;
    const endTime = device.preferences.quietHours.endTime;

    if (startTime && endTime) {
      // Simple time comparison (doesn't handle cross-midnight ranges)
      if (currentTime >= startTime && currentTime <= endTime) {
        return false; // In quiet hours
      }
    }

    return true;
  } catch (error) {
    logger.error('Failed to check quiet hours', { error, deviceId: device.id });
    return true; // Allow sending on error
  }
}

async function sendToDevice(device: DeviceRegistration, notification: PushNotification): Promise<any> {
  try {
    // Production Azure Notification Hubs integration
    const connectionString = process.env.NOTIFICATION_HUB_CONNECTION_STRING;
    const hubName = process.env.NOTIFICATION_HUB_NAME || 'hepzdocs';

    if (!connectionString) {
      throw new Error('Azure Notification Hub connection string not configured');
    }

    // Prepare notification payload based on platform
    let payload: string;
    let headers: { [key: string]: string } = {};

    switch (device.platform) {
      case 'ios':
        // Apple Push Notification Service (APNS) format
        const apnsPayload = {
          aps: {
            alert: {
              title: notification.notification.title,
              body: notification.notification.body
            },
            badge: notification.notification.badge || 1,
            sound: notification.notification.sound || 'default',
            'content-available': 1
          },
          data: notification.data || {}
        };
        payload = JSON.stringify(apnsPayload);
        headers['ServiceBusNotification-Format'] = 'apple';
        headers['ServiceBusNotification-Tags'] = device.tags?.join(',') || '';
        break;

      case 'android':
        // Firebase Cloud Messaging (FCM) format
        const fcmPayload = {
          notification: {
            title: notification.notification.title,
            body: notification.notification.body,
            icon: notification.notification.icon || 'default',
            sound: notification.notification.sound || 'default'
          },
          data: notification.data || {}
        };
        payload = JSON.stringify(fcmPayload);
        headers['ServiceBusNotification-Format'] = 'gcm';
        headers['ServiceBusNotification-Tags'] = device.tags?.join(',') || '';
        break;

      case 'windows':
        // Windows Notification Service (WNS) format
        const wnsPayload = `
          <toast>
            <visual>
              <binding template="ToastText02">
                <text id="1">${notification.notification.title}</text>
                <text id="2">${notification.notification.body}</text>
              </binding>
            </visual>
          </toast>
        `;
        payload = wnsPayload;
        headers['ServiceBusNotification-Format'] = 'windows';
        headers['ServiceBusNotification-Tags'] = device.tags?.join(',') || '';
        headers['X-WNS-Type'] = 'wns/toast';
        break;

      default:
        throw new Error(`Unsupported platform: ${device.platform}`);
    }

    // Send notification via Azure Notification Hubs REST API
    const result = await sendNotificationViaHub(connectionString, hubName, payload, headers, device);

    logger.info('Push notification sent via Azure Notification Hubs', {
      deviceId: device.id,
      platform: device.platform,
      hubName,
      notificationId: result.notificationId
    });

    return {
      success: true,
      messageId: result.notificationId,
      platform: device.platform,
      hubName
    };

  } catch (error) {
    logger.error('Failed to send to device', { error, deviceId: device.id });
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      platform: device.platform
    };
  }
}

/**
 * Send notification via Azure Notification Hubs REST API
 */
async function sendNotificationViaHub(
  connectionString: string,
  hubName: string,
  payload: string,
  headers: { [key: string]: string },
  device: DeviceRegistration
): Promise<{ notificationId: string }> {
  try {
    const crypto = require('crypto');

    // Parse connection string
    const endpoint = connectionString.match(/Endpoint=([^;]+)/)?.[1];
    const sharedAccessKeyName = connectionString.match(/SharedAccessKeyName=([^;]+)/)?.[1];
    const sharedAccessKey = connectionString.match(/SharedAccessKey=([^;]+)/)?.[1];

    if (!endpoint || !sharedAccessKeyName || !sharedAccessKey) {
      throw new Error('Invalid Notification Hub connection string');
    }

    // Build the URL
    const url = `${endpoint}${hubName}/messages/?direct&api-version=2015-01`;

    // Create SAS token for authentication
    const expiry = Math.floor(Date.now() / 1000) + 3600; // 1 hour
    const stringToSign = encodeURIComponent(url) + '\n' + expiry;
    const signature = crypto.createHmac('sha256', sharedAccessKey).update(stringToSign).digest('base64');
    const sasToken = `SharedAccessSignature sr=${encodeURIComponent(url)}&sig=${encodeURIComponent(signature)}&se=${expiry}&skn=${sharedAccessKeyName}`;

    // Prepare request headers
    const requestHeaders = {
      'Authorization': sasToken,
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(payload).toString(),
      ...headers
    };

    // Add device-specific targeting if available
    if (device.registrationId) {
      requestHeaders['ServiceBusNotification-DeviceHandle'] = device.registrationId;
    }

    // Make HTTP request to Azure Notification Hubs
    const response = await fetch(url, {
      method: 'POST',
      headers: requestHeaders,
      body: payload
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Azure Notification Hub request failed: ${response.status} ${response.statusText} - ${errorText}`);
    }

    // Extract notification ID from response headers
    const notificationId = response.headers.get('x-ms-tracking-id') ||
                          response.headers.get('TrackingId') ||
                          uuidv4();

    logger.info('Notification sent successfully via Azure Notification Hubs', {
      hubName,
      platform: device.platform,
      notificationId,
      statusCode: response.status
    });

    return { notificationId };

  } catch (error) {
    logger.error('Failed to send notification via Azure Notification Hubs REST API', {
      error: error instanceof Error ? error.message : String(error),
      hubName,
      platform: device.platform
    });
    throw error;
  }
}

// Register functions
app.http('push-notifications-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/push',
  handler: sendPushNotification
});

app.http('device-register', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'notifications/devices',
  handler: registerDevice
});
